<?php

namespace App\Http\Requests\DoorNumbering;

use Illuminate\Foundation\Http\FormRequest;

class GenerateDoorNumbersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'korpus_id' => 'required|integer|exists:korpuses,id',
            'regenerate_existing' => 'boolean'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'korpus_id.required' => 'Korpus ID is required',
            'korpus_id.exists' => 'The specified Korpus does not exist',
            'regenerate_existing.boolean' => 'Regenerate existing must be a boolean value'
        ];
    }
}
