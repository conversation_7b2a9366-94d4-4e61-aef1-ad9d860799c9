<?php

use App\Http\Controllers\BPayContoller;
use App\Http\Controllers\DoorNumberingController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\LogOpenDoorController;
use App\Http\Controllers\MemberController;
use App\Http\Controllers\OrcTagController;
use App\Http\Controllers\OrshinSuugchController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\PackageMonthController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::prefix('auth')->group(function () {
    Route::post('check-os', [OrshinSuugchController::class, 'checkOs']);
    Route::post('login-os', [OrshinSuugchController::class, 'loginOs']);
    // Route::post('login', [UserController::class, 'login']);
});

Route::middleware('auth:sanctum')->group(function () {
    // Route::get('aubairs', [UserController::class, 'aubairs']);
    // Route::get('auorcs/{bair_id}', [UserController::class, 'auorcs']);

    Route::prefix('orc-tags')->group(function () {
        Route::get('', [OrcTagController::class, 'index']);
        Route::post('', [OrcTagController::class, 'store']);
        Route::delete('{id}', [OrcTagController::class, 'destroy']);
    });

    Route::prefix('packages')->group(function () {
        Route::get('', [PackageController::class, 'index']);
        Route::get('total-price', [PackageController::class, 'getTotalPrice']);
    });
    Route::get('package-months', [PackageMonthController::class, 'index']);

    Route::get('me', [OrshinSuugchController::class, 'osme']);
    Route::get('device-codes/{device_code}', [OrshinSuugchController::class, 'checkDeviceCode']);
    Route::post('device-codes', [OrshinSuugchController::class, 'saveDeviceCode']);
    Route::get('orshin-suugch-toots', [OrshinSuugchController::class, 'getOsToots']);
    Route::get('orshin-suugch-toots/erkhs', [OrshinSuugchController::class, 'getOsTootErkhs']);
    Route::post('generate-access-code', [OrshinSuugchController::class, 'generateAccessCode']);
    Route::post('open-gate', [OrshinSuugchController::class, 'openGate']);

    Route::prefix('members')->group(function () {
        Route::get('', [MemberController::class, 'index']);
        Route::get('{invoice}', [MemberController::class, 'show']);
        Route::post('', [MemberController::class, 'store']);
        Route::put('{id}', [MemberController::class, 'update']);
        Route::delete('{id}', [MemberController::class, 'destroy']);
    });

    Route::prefix('invoices')->group(function () {
        Route::get('{id}', [InvoiceController::class, 'show']);
        Route::post('', [InvoiceController::class, 'createQpayInvoice']);
    });

    Route::prefix('log-open-doors')->group(function () {
        Route::post('', [LogOpenDoorController::class, 'createLogOpenDoor']);
        Route::post('change-status', [LogOpenDoorController::class, 'changeStatusLogOpenDoor']);
    });

    Route::prefix('bpay')->group(function () {
        Route::get('sumBillAmount', [BPayContoller::class, 'getSumBillAmount']);
        Route::get('bills', [BPayContoller::class, 'getSukhBills']);
        Route::post('invoice', [BPayContoller::class, 'createInvoice']);
        Route::get('invoice/{id}', [BPayContoller::class, 'checkInvoice']);
        Route::post('transaction', [BPayContoller::class, 'createTransaction']);
    });

    Route::prefix('door-numbering')->group(function () {
        Route::post('generate', [DoorNumberingController::class, 'generateDoorNumbers']);
        Route::get('validate/{korpusId}', [DoorNumberingController::class, 'validateConfiguration']);
        Route::get('statistics/{korpusId}', [DoorNumberingController::class, 'getStatistics']);
        Route::post('set-davhar-doors', [DoorNumberingController::class, 'setDavharDoorNumbers']);
        Route::post('set-single-door', [DoorNumberingController::class, 'setSingleDoorNumber']);
    });
});
