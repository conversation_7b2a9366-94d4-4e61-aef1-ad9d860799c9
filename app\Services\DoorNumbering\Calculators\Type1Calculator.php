<?php

namespace App\Services\DoorNumbering\Calculators;

use App\Models\Korpus;
use Illuminate\Support\Facades\Log;

/**
 * Type 1: Korpus-wise Consecutive Numbering (Variable Range)
 * 
 * This calculator handles numbering where each Korpus has a configurable range
 * of doors numbered consecutively across all Orcs and Davhars.
 */
class Type1Calculator implements DoorNumberingCalculatorInterface
{
    public function getNumberingType(): int
    {
        return 1;
    }

    public function calculateRanges(Korpus $korpus): array
    {
        Log::info('Type1Calculator: Starting range calculation', [
            'korpus_id' => $korpus->id,
            'begin_toot_number' => $korpus->begin_toot_number,
            'end_toot_number' => $korpus->end_toot_number
        ]);

        $orcs = $korpus->orcs()->orderBy('number')->get();
        $totalOrcs = $orcs->count();
        
        if ($totalOrcs === 0) {
            return ['orcs' => [], 'davhars' => []];
        }

        $totalDoors = $korpus->end_toot_number - $korpus->begin_toot_number + 1;
        $doorsPerOrc = intval($totalDoors / $totalOrcs);
        $remainingDoors = $totalDoors % $totalOrcs;

        $orcRanges = [];
        $davharRanges = [];
        $currentDoorNumber = $korpus->begin_toot_number;

        foreach ($orcs as $index => $orc) {
            // Calculate doors for this Orc (distribute remaining doors to first Orcs)
            $orcDoors = $doorsPerOrc + ($index < $remainingDoors ? 1 : 0);
            $orcBegin = $currentDoorNumber;
            $orcEnd = $currentDoorNumber + $orcDoors - 1;

            $orcRanges[] = [
                'orc_id' => $orc->id,
                'begin_toot_number' => $orcBegin,
                'end_toot_number' => $orcEnd
            ];

            // Calculate Davhar ranges within this Orc
            $davhars = $orc->davhars()->orderBy('order')->get();
            $totalDavhars = $davhars->count();

            if ($totalDavhars > 0) {
                $doorsPerDavhar = intval($orcDoors / $totalDavhars);
                $remainingOrcDoors = $orcDoors % $totalDavhars;
                $currentOrcDoor = $orcBegin;

                foreach ($davhars as $davharIndex => $davhar) {
                    // Calculate doors for this Davhar
                    $davharDoors = $doorsPerDavhar + ($davharIndex < $remainingOrcDoors ? 1 : 0);
                    $davharBegin = $currentOrcDoor;
                    $davharEnd = $currentOrcDoor + $davharDoors - 1;

                    $davharRanges[] = [
                        'davhar_id' => $davhar->id,
                        'orc_id' => $orc->id,
                        'begin_toot_number' => $davharBegin,
                        'end_toot_number' => $davharEnd
                    ];

                    $currentOrcDoor += $davharDoors;
                }
            }

            $currentDoorNumber += $orcDoors;
        }

        Log::info('Type1Calculator: Range calculation completed', [
            'korpus_id' => $korpus->id,
            'total_orcs' => $totalOrcs,
            'total_doors' => $totalDoors,
            'orc_ranges_count' => count($orcRanges),
            'davhar_ranges_count' => count($davharRanges)
        ]);

        return [
            'orcs' => $orcRanges,
            'davhars' => $davharRanges
        ];
    }

    public function validate(Korpus $korpus): array
    {
        $errors = [];

        // Check if begin and end toot numbers are set
        if (!$korpus->begin_toot_number || !$korpus->end_toot_number) {
            $errors[] = 'Begin and end toot numbers must be set for Type 1 numbering';
        }

        // Check if begin is less than end
        if ($korpus->begin_toot_number >= $korpus->end_toot_number) {
            $errors[] = 'Begin toot number must be less than end toot number';
        }

        // Check if there are Orcs
        $orcsCount = $korpus->orcs()->count();
        if ($orcsCount === 0) {
            $errors[] = 'At least one Orc must exist for door numbering';
        }

        // Check if all Orcs have Davhars
        $orcsWithoutDavhars = $korpus->orcs()->whereDoesntHave('davhars')->count();
        if ($orcsWithoutDavhars > 0) {
            $errors[] = "Found {$orcsWithoutDavhars} Orc(s) without Davhars";
        }

        // Check for reasonable door distribution
        if ($orcsCount > 0) {
            $totalDoors = $korpus->end_toot_number - $korpus->begin_toot_number + 1;
            if ($totalDoors < $orcsCount) {
                $errors[] = 'Total doors must be at least equal to the number of Orcs';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
