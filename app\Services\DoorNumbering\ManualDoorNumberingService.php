<?php

namespace App\Services\DoorNumbering;

use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

/**
 * Service for manual door numbering operations
 */
class ManualDoorNumberingService
{
    /**
     * Manually set door numbers for a specific Davhar
     *
     * @param Davhar $davhar
     * @param array $doorNumbers Array of door numbers to assign
     * @param bool $validateOnly Whether to only validate without applying changes
     * @return array
     * @throws ValidationException
     */
    public function setDavharDoorNumbers(Davhar $davhar, array $doorNumbers, bool $validateOnly = false): array
    {
        Log::info('ManualDoorNumberingService: Setting door numbers for Davhar', [
            'davhar_id' => $davhar->id,
            'door_numbers' => $doorNumbers,
            'validate_only' => $validateOnly
        ]);

        // Validate the door numbers
        $validation = $this->validateDavharDoorNumbers($davhar, $doorNumbers);
        if (!$validation['valid']) {
            throw ValidationException::withMessages($validation['errors']);
        }

        if ($validateOnly) {
            return [
                'success' => true,
                'validation' => $validation,
                'message' => 'Validation passed'
            ];
        }

        try {
            return DB::transaction(function () use ($davhar, $doorNumbers) {
                // Update Davhar range
                $minDoor = min($doorNumbers);
                $maxDoor = max($doorNumbers);
                
                $davhar->update([
                    'begin_toot_number' => $minDoor,
                    'end_toot_number' => $maxDoor
                ]);

                // Remove existing doors for this Davhar
                Toot::where('davhar_id', $davhar->id)->delete();

                // Create new doors
                $createdDoors = [];
                foreach ($doorNumbers as $doorNumber) {
                    $toot = Toot::create([
                        'korpus_id' => $davhar->orc->korpus_id,
                        'davhar_id' => $davhar->id,
                        'number' => $doorNumber
                    ]);
                    $createdDoors[] = $toot;
                }

                Log::info('ManualDoorNumberingService: Successfully set door numbers', [
                    'davhar_id' => $davhar->id,
                    'doors_created' => count($createdDoors)
                ]);

                return [
                    'success' => true,
                    'davhar_id' => $davhar->id,
                    'doors_created' => count($createdDoors),
                    'door_numbers' => $doorNumbers,
                    'range' => ['min' => $minDoor, 'max' => $maxDoor]
                ];
            });
        } catch (\Exception $e) {
            Log::error('ManualDoorNumberingService: Error setting door numbers', [
                'davhar_id' => $davhar->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Manually set a single door number
     *
     * @param Korpus $korpus
     * @param Davhar $davhar
     * @param int $oldNumber
     * @param int $newNumber
     * @return array
     * @throws ValidationException
     */
    public function setSingleDoorNumber(Korpus $korpus, Davhar $davhar, int $oldNumber, int $newNumber): array
    {
        Log::info('ManualDoorNumberingService: Setting single door number', [
            'korpus_id' => $korpus->id,
            'davhar_id' => $davhar->id,
            'old_number' => $oldNumber,
            'new_number' => $newNumber
        ]);

        // Validate the new door number
        $validation = $this->validateSingleDoorNumber($korpus, $davhar, $newNumber, $oldNumber);
        if (!$validation['valid']) {
            throw ValidationException::withMessages($validation['errors']);
        }

        try {
            return DB::transaction(function () use ($korpus, $davhar, $oldNumber, $newNumber) {
                // Find and update the door
                $toot = Toot::where('korpus_id', $korpus->id)
                    ->where('davhar_id', $davhar->id)
                    ->where('number', $oldNumber)
                    ->first();

                if (!$toot) {
                    throw new \Exception("Door with number {$oldNumber} not found");
                }

                $toot->update(['number' => $newNumber]);

                // Update Davhar range if necessary
                $this->updateDavharRange($davhar);

                Log::info('ManualDoorNumberingService: Successfully updated door number', [
                    'toot_id' => $toot->id,
                    'old_number' => $oldNumber,
                    'new_number' => $newNumber
                ]);

                return [
                    'success' => true,
                    'toot_id' => $toot->id,
                    'old_number' => $oldNumber,
                    'new_number' => $newNumber
                ];
            });
        } catch (\Exception $e) {
            Log::error('ManualDoorNumberingService: Error updating door number', [
                'korpus_id' => $korpus->id,
                'davhar_id' => $davhar->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate door numbers for a Davhar
     *
     * @param Davhar $davhar
     * @param array $doorNumbers
     * @return array
     */
    protected function validateDavharDoorNumbers(Davhar $davhar, array $doorNumbers): array
    {
        $errors = [];
        $korpus = $davhar->orc->korpus;

        // Check for empty array
        if (empty($doorNumbers)) {
            $errors['door_numbers'] = ['At least one door number must be provided'];
            return ['valid' => false, 'errors' => $errors];
        }

        // Check for duplicates within the array
        if (count($doorNumbers) !== count(array_unique($doorNumbers))) {
            $errors['door_numbers'] = ['Duplicate door numbers are not allowed'];
        }

        // Check for conflicts with existing doors in other Davhars
        $existingDoors = Toot::where('korpus_id', $korpus->id)
            ->where('davhar_id', '!=', $davhar->id)
            ->whereIn('number', $doorNumbers)
            ->pluck('number')
            ->toArray();

        if (!empty($existingDoors)) {
            $errors['door_numbers'] = ["Door numbers already exist in other Davhars: " . implode(', ', $existingDoors)];
        }

        // Validate against numbering type constraints
        $typeValidation = $this->validateAgainstNumberingType($korpus, $davhar, $doorNumbers);
        if (!$typeValidation['valid']) {
            $errors = array_merge($errors, $typeValidation['errors']);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate a single door number
     *
     * @param Korpus $korpus
     * @param Davhar $davhar
     * @param int $newNumber
     * @param int|null $excludeNumber
     * @return array
     */
    protected function validateSingleDoorNumber(Korpus $korpus, Davhar $davhar, int $newNumber, ?int $excludeNumber = null): array
    {
        $errors = [];

        // Check for conflicts with existing doors
        $query = Toot::where('korpus_id', $korpus->id)
            ->where('number', $newNumber);

        if ($excludeNumber !== null) {
            $query->where('number', '!=', $excludeNumber);
        }

        if ($query->exists()) {
            $errors['new_number'] = ['Door number already exists'];
        }

        // Validate against numbering type constraints
        $typeValidation = $this->validateAgainstNumberingType($korpus, $davhar, [$newNumber]);
        if (!$typeValidation['valid']) {
            $errors = array_merge($errors, $typeValidation['errors']);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate door numbers against numbering type constraints
     *
     * @param Korpus $korpus
     * @param Davhar $davhar
     * @param array $doorNumbers
     * @return array
     */
    protected function validateAgainstNumberingType(Korpus $korpus, Davhar $davhar, array $doorNumbers): array
    {
        $errors = [];

        switch ($korpus->numbering_type) {
            case 1: // Type 1: Korpus-wise consecutive
                foreach ($doorNumbers as $number) {
                    if ($number < $korpus->begin_toot_number || $number > $korpus->end_toot_number) {
                        $errors['numbering_type'] = ["Door number {$number} is outside Korpus range ({$korpus->begin_toot_number}-{$korpus->end_toot_number})"];
                        break;
                    }
                }
                break;

            case 2: // Type 2: Orc-wise consecutive
                $orc = $davhar->orc;
                if ($orc->begin_toot_number && $orc->end_toot_number) {
                    foreach ($doorNumbers as $number) {
                        if ($number < $orc->begin_toot_number || $number > $orc->end_toot_number) {
                            $errors['numbering_type'] = ["Door number {$number} is outside Orc range ({$orc->begin_toot_number}-{$orc->end_toot_number})"];
                            break;
                        }
                    }
                }
                break;

            case 3: // Type 3: Floor-wise consecutive
                if ($korpus->digit_multiplier && $davhar->floor_number) {
                    $expectedMin = ($davhar->floor_number * $korpus->digit_multiplier) + 1;
                    $expectedMax = ($davhar->floor_number + 1) * $korpus->digit_multiplier;
                    
                    foreach ($doorNumbers as $number) {
                        if ($number < $expectedMin || $number >= $expectedMax) {
                            $errors['numbering_type'] = ["Door number {$number} doesn't match floor-based format for floor {$davhar->floor_number} (expected range: {$expectedMin}-" . ($expectedMax - 1) . ")"];
                            break;
                        }
                    }
                }
                break;
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Update Davhar range based on its current doors
     *
     * @param Davhar $davhar
     * @return void
     */
    protected function updateDavharRange(Davhar $davhar): void
    {
        $doorNumbers = Toot::where('davhar_id', $davhar->id)
            ->pluck('number')
            ->toArray();

        if (!empty($doorNumbers)) {
            $davhar->update([
                'begin_toot_number' => min($doorNumbers),
                'end_toot_number' => max($doorNumbers)
            ]);
        }
    }
}
