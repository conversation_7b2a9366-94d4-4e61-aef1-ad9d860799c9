<?php

namespace App\Services\DoorNumbering\Calculators;

use App\Models\Korpus;

interface DoorNumberingCalculatorInterface
{
    /**
     * Calculate door number ranges for all levels (Orcs and Davhars)
     *
     * @param Korpus $korpus
     * @return array Array containing 'orcs' and 'davhars' ranges
     */
    public function calculateRanges(Korpus $korpus): array;

    /**
     * Validate the configuration for this numbering type
     *
     * @param Korpus $korpus
     * @return array Validation results with 'valid' boolean and 'errors' array
     */
    public function validate(Korpus $korpus): array;

    /**
     * Get the numbering type this calculator handles
     *
     * @return int
     */
    public function getNumberingType(): int;
}
