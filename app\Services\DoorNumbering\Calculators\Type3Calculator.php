<?php

namespace App\Services\DoorNumbering\Calculators;

use App\Models\Korpus;
use Illuminate\Support\Facades\Log;

/**
 * Type 3: Floor-wise Consecutive Numbering
 * 
 * This calculator handles numbering where door numbers incorporate the floor
 * number as a prefix with configurable digit formats.
 */
class Type3Calculator implements DoorNumberingCalculatorInterface
{
    public function getNumberingType(): int
    {
        return 3;
    }

    public function calculateRanges(Korpus $korpus): array
    {
        Log::info('Type3Calculator: Starting range calculation', [
            'korpus_id' => $korpus->id,
            'digit_multiplier' => $korpus->digit_multiplier
        ]);

        $orcs = $korpus->orcs()->orderBy('number')->get();
        $orcRanges = [];
        $davharRanges = [];

        foreach ($orcs as $orc) {
            $davhars = $orc->davhars()->orderBy('order')->get();
            
            if ($davhars->isEmpty()) {
                continue;
            }

            // For Type 3, Orc ranges are not meaningful since doors are floor-based
            // But we still need to set them for consistency
            $minFloorNumber = $davhars->min('order'); // Use order as floor number if floor_number not set
            $maxFloorNumber = $davhars->max('order');
            $doorsPerFloor = $this->calculateDoorsPerFloor($orc);

            $minDoorNumber = ($minFloorNumber * $korpus->digit_multiplier) + 1;
            $maxDoorNumber = ($maxFloorNumber * $korpus->digit_multiplier) + $doorsPerFloor;

            $orcRanges[] = [
                'orc_id' => $orc->id,
                'begin_toot_number' => $minDoorNumber,
                'end_toot_number' => $maxDoorNumber
            ];

            // Calculate Davhar ranges (floor-based)
            foreach ($davhars as $index => $davhar) {
                $floorNumber = $davhar->floor_number ?? ($index + 1); // Use floor_number or fallback to order
                
                $floorBegin = ($floorNumber * $korpus->digit_multiplier) + 1;
                $floorEnd = ($floorNumber * $korpus->digit_multiplier) + $doorsPerFloor;

                $davharRanges[] = [
                    'davhar_id' => $davhar->id,
                    'orc_id' => $orc->id,
                    'begin_toot_number' => $floorBegin,
                    'end_toot_number' => $floorEnd,
                    'floor_number' => $floorNumber
                ];
            }
        }

        Log::info('Type3Calculator: Range calculation completed', [
            'korpus_id' => $korpus->id,
            'total_orcs' => count($orcRanges),
            'orc_ranges_count' => count($orcRanges),
            'davhar_ranges_count' => count($davharRanges)
        ]);

        return [
            'orcs' => $orcRanges,
            'davhars' => $davharRanges
        ];
    }

    /**
     * Calculate doors per floor for Type 3 numbering
     *
     * @param \App\Models\Orc $orc
     * @return int
     */
    protected function calculateDoorsPerFloor($orc): int
    {
        // Default doors per floor (can be made configurable)
        $defaultDoorsPerFloor = 6;
        
        // You could also calculate this based on existing door configuration
        // or make it configurable per Orc/Davhar
        
        return $defaultDoorsPerFloor;
    }

    public function validate(Korpus $korpus): array
    {
        $errors = [];

        // Check if digit_multiplier is set and valid
        if (!$korpus->digit_multiplier || !in_array($korpus->digit_multiplier, [10, 100, 1000, 10000])) {
            $errors[] = 'Digit multiplier must be set to 10, 100, 1000, or 10000 for Type 3 numbering';
        }

        // Check if there are Orcs
        $orcsCount = $korpus->orcs()->count();
        if ($orcsCount === 0) {
            $errors[] = 'At least one Orc must exist for door numbering';
        }

        // Check if all Orcs have Davhars
        $orcsWithoutDavhars = $korpus->orcs()->whereDoesntHave('davhars')->count();
        if ($orcsWithoutDavhars > 0) {
            $errors[] = "Found {$orcsWithoutDavhars} Orc(s) without Davhars";
        }

        // Validate floor numbers and digit capacity
        if ($korpus->digit_multiplier) {
            $maxDoorsPerFloor = $korpus->digit_multiplier - 1;
            $defaultDoorsPerFloor = 6; // This should match calculateDoorsPerFloor logic
            
            if ($defaultDoorsPerFloor > $maxDoorsPerFloor) {
                $errors[] = "Doors per floor ({$defaultDoorsPerFloor}) exceeds digit capacity ({$maxDoorsPerFloor}) for multiplier {$korpus->digit_multiplier}";
            }
        }

        // Check for reasonable floor numbers
        foreach ($korpus->orcs as $orc) {
            $davhars = $orc->davhars;
            if ($davhars->isEmpty()) {
                $errors[] = "Orc {$orc->number} has no Davhars";
                continue;
            }

            // Check for duplicate floor numbers within the same Orc
            $floorNumbers = $davhars->pluck('floor_number')->filter()->toArray();
            if (count($floorNumbers) !== count(array_unique($floorNumbers))) {
                $errors[] = "Orc {$orc->number} has duplicate floor numbers";
            }

            // Check for reasonable floor number range
            $maxFloorNumber = max($floorNumbers ?: [count($davhars)]);
            if ($maxFloorNumber > 99 && $korpus->digit_multiplier < 1000) {
                $errors[] = "Floor number {$maxFloorNumber} in Orc {$orc->number} requires at least 4-digit format (digit_multiplier >= 1000)";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
