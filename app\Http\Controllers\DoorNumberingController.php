<?php

namespace App\Http\Controllers;

use App\Models\Korpus;
use App\Models\Davhar;
use App\Services\DoorNumbering\DoorNumberingService;
use App\Services\DoorNumbering\ManualDoorNumberingService;
use App\Http\Requests\DoorNumbering\GenerateDoorNumbersRequest;
use App\Http\Requests\DoorNumbering\SetDavharDoorNumbersRequest;
use App\Http\Requests\DoorNumbering\SetSingleDoorNumberRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

/**
 * Controller for door numbering operations
 */
class DoorNumberingController extends Controller
{
    protected DoorNumberingService $doorNumberingService;
    protected ManualDoorNumberingService $manualDoorNumberingService;

    public function __construct(
        DoorNumberingService $doorNumberingService,
        ManualDoorNumberingService $manualDoorNumberingService
    ) {
        $this->doorNumberingService = $doorNumberingService;
        $this->manualDoorNumberingService = $manualDoorNumberingService;
    }

    /**
     * Generate automatic door numbers for a Korpus
     *
     * @param GenerateDoorNumbersRequest $request
     * @return JsonResponse
     */
    public function generateDoorNumbers(GenerateDoorNumbersRequest $request): JsonResponse
    {
        $korpusId = $request->input('korpus_id');
        $regenerateExisting = $request->input('regenerate_existing', false);

        $korpus = Korpus::findOrFail($korpusId);

        Log::info('DoorNumberingController: Generate door numbers request', [
            'korpus_id' => $korpusId,
            'regenerate_existing' => $regenerateExisting,
            'user_id' => auth()->id()
        ]);

        // Validate configuration first
        $validation = $this->doorNumberingService->validateKorpusConfiguration($korpus);
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid Korpus configuration for door numbering',
                'errors' => $validation['errors']
            ], 422);
        }

        // Generate door numbers
        $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus, $regenerateExisting);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Door numbers generated successfully',
                'data' => $result
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate door numbers',
                'error' => $result['error'] ?? 'Unknown error'
            ], 500);
        }
    }

    /**
     * Validate Korpus configuration for door numbering
     *
     * @param int $korpusId
     * @return JsonResponse
     */
    public function validateConfiguration(int $korpusId): JsonResponse
    {
        $korpus = Korpus::findOrFail($korpusId);

        $validation = $this->doorNumberingService->validateKorpusConfiguration($korpus);

        return response()->json([
            'success' => true,
            'korpus_id' => $korpusId,
            'validation' => $validation
        ]);
    }

    /**
     * Get door numbering statistics for a Korpus
     *
     * @param int $korpusId
     * @return JsonResponse
     */
    public function getStatistics(int $korpusId): JsonResponse
    {
        $korpus = Korpus::findOrFail($korpusId);

        $statistics = $this->doorNumberingService->getKorpusStatistics($korpus);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Manually set door numbers for a Davhar
     *
     * @param SetDavharDoorNumbersRequest $request
     * @return JsonResponse
     */
    public function setDavharDoorNumbers(SetDavharDoorNumbersRequest $request): JsonResponse
    {
        $davharId = $request->input('davhar_id');
        $doorNumbers = $request->input('door_numbers');
        $validateOnly = $request->input('validate_only', false);

        $davhar = Davhar::findOrFail($davharId);

        Log::info('DoorNumberingController: Set Davhar door numbers request', [
            'davhar_id' => $davharId,
            'door_numbers' => $doorNumbers,
            'validate_only' => $validateOnly,
            'user_id' => auth()->id()
        ]);

        try {
            $result = $this->manualDoorNumberingService->setDavharDoorNumbers(
                $davhar,
                $doorNumbers,
                $validateOnly
            );

            return response()->json([
                'success' => true,
                'message' => $validateOnly ? 'Validation completed' : 'Door numbers set successfully',
                'data' => $result
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('DoorNumberingController: Error setting Davhar door numbers', [
                'davhar_id' => $davharId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to set door numbers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Manually set a single door number
     *
     * @param SetSingleDoorNumberRequest $request
     * @return JsonResponse
     */
    public function setSingleDoorNumber(SetSingleDoorNumberRequest $request): JsonResponse
    {
        $korpusId = $request->input('korpus_id');
        $davharId = $request->input('davhar_id');
        $oldNumber = $request->input('old_number');
        $newNumber = $request->input('new_number');

        $korpus = Korpus::findOrFail($korpusId);
        $davhar = Davhar::findOrFail($davharId);

        Log::info('DoorNumberingController: Set single door number request', [
            'korpus_id' => $korpusId,
            'davhar_id' => $davharId,
            'old_number' => $oldNumber,
            'new_number' => $newNumber,
            'user_id' => auth()->id()
        ]);

        try {
            $result = $this->manualDoorNumberingService->setSingleDoorNumber(
                $korpus,
                $davhar,
                $oldNumber,
                $newNumber
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Door number updated successfully',
                    'data' => $result
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update door number',
                    'error' => $result['error']
                ], 500);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('DoorNumberingController: Error setting single door number', [
                'korpus_id' => $korpusId,
                'davhar_id' => $davharId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update door number',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
