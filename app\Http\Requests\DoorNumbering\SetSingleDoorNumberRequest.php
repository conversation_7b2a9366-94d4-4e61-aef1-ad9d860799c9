<?php

namespace App\Http\Requests\DoorNumbering;

use Illuminate\Foundation\Http\FormRequest;

class SetSingleDoorNumberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'korpus_id' => 'required|integer|exists:korpuses,id',
            'davhar_id' => 'required|integer|exists:davhars,id',
            'old_number' => 'required|integer|min:1',
            'new_number' => 'required|integer|min:1'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'korpus_id.required' => 'Korpus ID is required',
            'korpus_id.exists' => 'The specified Korpus does not exist',
            'davhar_id.required' => 'Davhar ID is required',
            'davhar_id.exists' => 'The specified Davhar does not exist',
            'old_number.required' => 'Old door number is required',
            'old_number.integer' => 'Old door number must be an integer',
            'old_number.min' => 'Old door number must be at least 1',
            'new_number.required' => 'New door number is required',
            'new_number.integer' => 'New door number must be an integer',
            'new_number.min' => 'New door number must be at least 1'
        ];
    }
}
