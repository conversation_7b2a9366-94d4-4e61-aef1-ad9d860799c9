<?php

namespace App\Observers;

use App\Models\Davhar;
use App\Services\DavharSyncService;
use App\Services\DoorNumbering\DoorNumberingService;
use Illuminate\Support\Facades\Log;

class DavharObserver
{
    protected DavharSyncService $davharSyncService;
    protected DoorNumberingService $doorNumberingService;

    public function __construct(
        DavharSyncService $davharSyncService,
        DoorNumberingService $doorNumberingService
    ) {
        $this->davharSyncService = $davharSyncService;
        $this->doorNumberingService = $doorNumberingService;
    }

    /**
     * Handle the Davhar "created" event.
     *
     * @param Davhar $davhar
     * @return void
     */
    public function created(Davhar $davhar): void
    {
        Log::info('DavharObserver: Davhar created event triggered', [
            'davhar_id' => $davhar->id,
            'davhar_number' => $davhar->number,
            'orc_id' => $davhar->orc_id
        ]);

        // Sync with CVSecurity service
        $this->davharSyncService->syncCreate($davhar);

        // Trigger door numbering calculation
        $this->triggerDoorNumbering($davhar, 'davhar_created');
    }

    /**
     * Handle the Davhar "updated" event.
     *
     * @param Davhar $davhar
     * @return void
     */
    public function updated(Davhar $davhar): void
    {
        // Skip sync if only the code field was updated (to avoid infinite loops)
        if ($davhar->wasChanged(Davhar::CODE) && count($davhar->getChanges()) === 1) {
            Log::debug('DavharObserver: Skipping sync for code-only update', [
                'davhar_id' => $davhar->id
            ]);
            return;
        }

        Log::info('DavharObserver: Davhar updated event triggered', [
            'davhar_id' => $davhar->id,
            'davhar_number' => $davhar->number,
            'orc_id' => $davhar->orc_id,
            'changed_fields' => array_keys($davhar->getChanges())
        ]);

        // Sync with CVSecurity service
        $this->davharSyncService->syncUpdate($davhar);

        // Check if floor_number was changed (relevant for Type 3 numbering)
        if ($davhar->wasChanged('floor_number')) {
            $this->triggerDoorNumbering($davhar, 'davhar_updated');
        }
    }

    /**
     * Handle the Davhar "deleted" event.
     *
     * @param Davhar $davhar
     * @return void
     */
    public function deleted(Davhar $davhar): void
    {
        Log::info('DavharObserver: Davhar deleted event triggered', [
            'davhar_id' => $davhar->id,
            'davhar_number' => $davhar->number,
            'orc_id' => $davhar->orc_id,
            'cv_code' => $davhar->code
        ]);

        // Sync with CVSecurity service
        $this->davharSyncService->syncDelete($davhar);

        // Trigger door numbering recalculation after deletion
        $this->triggerDoorNumbering($davhar, 'davhar_deleted');
    }

    /**
     * Handle the Davhar "retrieved" event.
     * This can be used for read operations if needed.
     *
     * @param Davhar $davhar
     * @return void
     */
    public function retrieved(Davhar $davhar): void
    {
        // Optionally sync read operations
        // Uncomment the line below if you want to sync on every read
        // $this->davharSyncService->syncRead($davhar);
    }

    /**
     * Trigger door numbering calculation for a Davhar's Korpus
     *
     * @param Davhar $davhar
     * @param string $trigger
     * @return void
     */
    protected function triggerDoorNumbering(Davhar $davhar, string $trigger): void
    {
        try {
            $korpus = $davhar->orc->korpus;
            if (!$korpus) {
                return;
            }

            Log::info('DavharObserver: Triggering door numbering calculation', [
                'davhar_id' => $davhar->id,
                'korpus_id' => $korpus->id,
                'trigger' => $trigger
            ]);

            $regenerateExisting = in_array($trigger, ['davhar_updated', 'davhar_deleted']);
            $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus, $regenerateExisting);

            if ($result['success']) {
                Log::info('DavharObserver: Door numbering completed successfully', [
                    'davhar_id' => $davhar->id,
                    'korpus_id' => $korpus->id,
                    'trigger' => $trigger,
                    'result' => $result
                ]);
            } else {
                Log::error('DavharObserver: Door numbering failed', [
                    'davhar_id' => $davhar->id,
                    'korpus_id' => $korpus->id,
                    'trigger' => $trigger,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('DavharObserver: Exception during door numbering', [
                'davhar_id' => $davhar->id,
                'trigger' => $trigger,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
