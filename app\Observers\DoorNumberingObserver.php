<?php

namespace App\Observers;

use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Services\DoorNumbering\DoorNumberingService;
use Illuminate\Support\Facades\Log;

/**
 * Observer for automatic door numbering when hierarchical models are updated
 */
class DoorNumberingObserver
{
    protected DoorNumberingService $doorNumberingService;

    public function __construct(DoorNumberingService $doorNumberingService)
    {
        $this->doorNumberingService = $doorNumberingService;
    }

    /**
     * Handle Korpus created event
     */
    public function korpusCreated(Korpus $korpus): void
    {
        Log::info('DoorNumberingObserver: Korpus created, checking for automatic numbering', [
            'korpus_id' => $korpus->id,
            'numbering_type' => $korpus->numbering_type
        ]);

        // Only auto-generate if the Korpus has Orcs and Davhars
        if ($korpus->orcs()->exists() && $this->hasAnyDavhars($korpus)) {
            $this->triggerDoorNumbering($korpus, 'korpus_created');
        }
    }

    /**
     * Handle Korpus updated event
     */
    public function korpusUpdated(Korpus $korpus): void
    {
        // Check if door numbering related fields were changed
        $relevantFields = ['numbering_type', 'digit_multiplier', 'begin_toot_number', 'end_toot_number'];
        $changedFields = array_keys($korpus->getChanges());
        $hasRelevantChanges = !empty(array_intersect($relevantFields, $changedFields));

        if ($hasRelevantChanges) {
            Log::info('DoorNumberingObserver: Korpus updated with relevant changes', [
                'korpus_id' => $korpus->id,
                'changed_fields' => $changedFields
            ]);

            if ($korpus->orcs()->exists() && $this->hasAnyDavhars($korpus)) {
                $this->triggerDoorNumbering($korpus, 'korpus_updated', true);
            }
        }
    }

    /**
     * Handle Orc created event
     */
    public function orcCreated(Orc $orc): void
    {
        Log::info('DoorNumberingObserver: Orc created, checking for automatic numbering', [
            'orc_id' => $orc->id,
            'korpus_id' => $orc->korpus_id
        ]);

        $korpus = $orc->korpus;
        if ($korpus && $orc->davhars()->exists()) {
            $this->triggerDoorNumbering($korpus, 'orc_created');
        }
    }

    /**
     * Handle Orc updated event
     */
    public function orcUpdated(Orc $orc): void
    {
        // For Type 2 numbering, Orc changes might affect door ranges
        $korpus = $orc->korpus;
        if ($korpus && $korpus->numbering_type == 2) {
            $relevantFields = ['begin_toot_number', 'end_toot_number'];
            $changedFields = array_keys($orc->getChanges());
            $hasRelevantChanges = !empty(array_intersect($relevantFields, $changedFields));

            if ($hasRelevantChanges && $orc->davhars()->exists()) {
                Log::info('DoorNumberingObserver: Orc updated with relevant changes for Type 2', [
                    'orc_id' => $orc->id,
                    'korpus_id' => $orc->korpus_id,
                    'changed_fields' => $changedFields
                ]);

                $this->triggerDoorNumbering($korpus, 'orc_updated', true);
            }
        }
    }

    /**
     * Handle Davhar created event
     */
    public function davharCreated(Davhar $davhar): void
    {
        Log::info('DoorNumberingObserver: Davhar created, triggering door numbering', [
            'davhar_id' => $davhar->id,
            'orc_id' => $davhar->orc_id
        ]);

        $korpus = $davhar->orc->korpus;
        if ($korpus) {
            $this->triggerDoorNumbering($korpus, 'davhar_created');
        }
    }

    /**
     * Handle Davhar updated event
     */
    public function davharUpdated(Davhar $davhar): void
    {
        $korpus = $davhar->orc->korpus;
        if ($korpus && $korpus->numbering_type == 3) {
            // For Type 3, floor_number changes affect door numbering
            if ($davhar->wasChanged('floor_number')) {
                Log::info('DoorNumberingObserver: Davhar floor_number updated for Type 3', [
                    'davhar_id' => $davhar->id,
                    'orc_id' => $davhar->orc_id,
                    'new_floor_number' => $davhar->floor_number
                ]);

                $this->triggerDoorNumbering($korpus, 'davhar_updated', true);
            }
        }
    }

    /**
     * Handle Davhar deleted event
     */
    public function davharDeleted(Davhar $davhar): void
    {
        Log::info('DoorNumberingObserver: Davhar deleted, triggering door numbering recalculation', [
            'davhar_id' => $davhar->id,
            'orc_id' => $davhar->orc_id
        ]);

        $korpus = $davhar->orc->korpus;
        if ($korpus) {
            $this->triggerDoorNumbering($korpus, 'davhar_deleted', true);
        }
    }

    /**
     * Trigger door numbering calculation for a Korpus
     */
    protected function triggerDoorNumbering(Korpus $korpus, string $trigger, bool $regenerateExisting = false): void
    {
        try {
            Log::info('DoorNumberingObserver: Triggering door numbering calculation', [
                'korpus_id' => $korpus->id,
                'trigger' => $trigger,
                'regenerate_existing' => $regenerateExisting
            ]);

            $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus, $regenerateExisting);

            if ($result['success']) {
                Log::info('DoorNumberingObserver: Door numbering completed successfully', [
                    'korpus_id' => $korpus->id,
                    'trigger' => $trigger,
                    'result' => $result
                ]);
            } else {
                Log::error('DoorNumberingObserver: Door numbering failed', [
                    'korpus_id' => $korpus->id,
                    'trigger' => $trigger,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('DoorNumberingObserver: Exception during door numbering', [
                'korpus_id' => $korpus->id,
                'trigger' => $trigger,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Check if a Korpus has any Davhars
     */
    protected function hasAnyDavhars(Korpus $korpus): bool
    {
        return $korpus->orcs()
            ->whereHas('davhars')
            ->exists();
    }
}
