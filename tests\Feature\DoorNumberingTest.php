<?php

namespace Tests\Feature;

use App\Models\Sukh;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use App\Services\DoorNumbering\DoorNumberingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DoorNumberingTest extends TestCase
{
    use RefreshDatabase;

    protected DoorNumberingService $doorNumberingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->doorNumberingService = app(DoorNumberingService::class);
    }

    /** @test */
    public function it_can_calculate_type_1_door_numbers()
    {
        // Create test data
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create([
            'bair_id' => $bair->id,
            'numbering_type' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 12
        ]);

        // Create 2 Orcs
        $orc1 = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $orc2 = Orc::create(['korpus_id' => $korpus->id, 'number' => '2']);

        // Create 2 Davhars per Orc
        $davhar1 = Davhar::create(['orc_id' => $orc1->id, 'number' => '1', 'order' => 1]);
        $davhar2 = Davhar::create(['orc_id' => $orc1->id, 'number' => '2', 'order' => 2]);
        $davhar3 = Davhar::create(['orc_id' => $orc2->id, 'number' => '1', 'order' => 1]);
        $davhar4 = Davhar::create(['orc_id' => $orc2->id, 'number' => '2', 'order' => 2]);

        // Calculate door numbers
        $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus);

        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['numbering_type']);

        // Verify Orc ranges
        $orc1->refresh();
        $orc2->refresh();
        $this->assertEquals(1, $orc1->begin_toot_number);
        $this->assertEquals(6, $orc1->end_toot_number);
        $this->assertEquals(7, $orc2->begin_toot_number);
        $this->assertEquals(12, $orc2->end_toot_number);

        // Verify Davhar ranges
        $davhar1->refresh();
        $davhar2->refresh();
        $davhar3->refresh();
        $davhar4->refresh();

        $this->assertEquals(1, $davhar1->begin_toot_number);
        $this->assertEquals(3, $davhar1->end_toot_number);
        $this->assertEquals(4, $davhar2->begin_toot_number);
        $this->assertEquals(6, $davhar2->end_toot_number);
        $this->assertEquals(7, $davhar3->begin_toot_number);
        $this->assertEquals(9, $davhar3->end_toot_number);
        $this->assertEquals(10, $davhar4->begin_toot_number);
        $this->assertEquals(12, $davhar4->end_toot_number);

        // Verify doors were created
        $totalDoors = Toot::where('korpus_id', $korpus->id)->count();
        $this->assertEquals(12, $totalDoors);
    }

    /** @test */
    public function it_can_calculate_type_2_door_numbers()
    {
        // Create test data
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create([
            'bair_id' => $bair->id,
            'numbering_type' => 2
        ]);

        // Create 2 Orcs
        $orc1 = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $orc2 = Orc::create(['korpus_id' => $korpus->id, 'number' => '2']);

        // Create 2 Davhars per Orc
        Davhar::create(['orc_id' => $orc1->id, 'number' => '1', 'order' => 1]);
        Davhar::create(['orc_id' => $orc1->id, 'number' => '2', 'order' => 2]);
        Davhar::create(['orc_id' => $orc2->id, 'number' => '1', 'order' => 1]);
        Davhar::create(['orc_id' => $orc2->id, 'number' => '2', 'order' => 2]);

        // Calculate door numbers
        $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus);

        $this->assertTrue($result['success']);
        $this->assertEquals(2, $result['numbering_type']);

        // Verify Orc ranges (each starts from 1)
        $orc1->refresh();
        $orc2->refresh();
        $this->assertEquals(1, $orc1->begin_toot_number);
        $this->assertEquals(12, $orc1->end_toot_number); // 2 davhars * 6 doors each
        $this->assertEquals(1, $orc2->begin_toot_number);
        $this->assertEquals(12, $orc2->end_toot_number);

        // Verify doors were created with independent numbering
        $orc1Doors = Toot::whereIn('davhar_id', $orc1->davhars->pluck('id'))->pluck('number')->sort()->values();
        $orc2Doors = Toot::whereIn('davhar_id', $orc2->davhars->pluck('id'))->pluck('number')->sort()->values();

        $this->assertEquals(collect(range(1, 12)), $orc1Doors);
        $this->assertEquals(collect(range(1, 12)), $orc2Doors);
    }

    /** @test */
    public function it_can_calculate_type_3_door_numbers()
    {
        // Create test data
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create([
            'bair_id' => $bair->id,
            'numbering_type' => 3,
            'digit_multiplier' => 100 // 3-digit format
        ]);

        // Create 1 Orc
        $orc = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);

        // Create 3 Davhars (floors)
        $davhar1 = Davhar::create(['orc_id' => $orc->id, 'number' => '1', 'order' => 1, 'floor_number' => 1]);
        $davhar2 = Davhar::create(['orc_id' => $orc->id, 'number' => '2', 'order' => 2, 'floor_number' => 2]);
        $davhar3 = Davhar::create(['orc_id' => $orc->id, 'number' => '3', 'order' => 3, 'floor_number' => 3]);

        // Calculate door numbers
        $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus);

        $this->assertTrue($result['success']);
        $this->assertEquals(3, $result['numbering_type']);

        // Verify Davhar ranges (floor-based)
        $davhar1->refresh();
        $davhar2->refresh();
        $davhar3->refresh();

        $this->assertEquals(101, $davhar1->begin_toot_number); // Floor 1: 101-106
        $this->assertEquals(106, $davhar1->end_toot_number);
        $this->assertEquals(201, $davhar2->begin_toot_number); // Floor 2: 201-206
        $this->assertEquals(206, $davhar2->end_toot_number);
        $this->assertEquals(301, $davhar3->begin_toot_number); // Floor 3: 301-306
        $this->assertEquals(306, $davhar3->end_toot_number);

        // Verify doors were created with floor-based numbering
        $floor1Doors = Toot::where('davhar_id', $davhar1->id)->pluck('number')->sort()->values();
        $floor2Doors = Toot::where('davhar_id', $davhar2->id)->pluck('number')->sort()->values();
        $floor3Doors = Toot::where('davhar_id', $davhar3->id)->pluck('number')->sort()->values();

        $this->assertEquals(collect([101, 102, 103, 104, 105, 106]), $floor1Doors);
        $this->assertEquals(collect([201, 202, 203, 204, 205, 206]), $floor2Doors);
        $this->assertEquals(collect([301, 302, 303, 304, 305, 306]), $floor3Doors);
    }

    /** @test */
    public function it_validates_korpus_configuration()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        
        // Test invalid Type 1 configuration (no door range)
        $korpus = Korpus::factory()->create([
            'bair_id' => $bair->id,
            'numbering_type' => 1,
            'begin_toot_number' => 0,
            'end_toot_number' => 0
        ]);

        $validation = $this->doorNumberingService->validateKorpusConfiguration($korpus);
        $this->assertFalse($validation['valid']);
        $this->assertContains('Begin and end toot numbers must be set for Type 1 numbering', $validation['errors']);

        // Test invalid Type 3 configuration (no digit multiplier)
        $korpus->update([
            'numbering_type' => 3,
            'digit_multiplier' => 0
        ]);

        $validation = $this->doorNumberingService->validateKorpusConfiguration($korpus);
        $this->assertFalse($validation['valid']);
        $this->assertContains('Digit multiplier must be set to 10, 100, 1000, or 10000 for Type 3 numbering', $validation['errors']);
    }

    /** @test */
    public function it_gets_korpus_statistics()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create([
            'bair_id' => $bair->id,
            'numbering_type' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6
        ]);

        $orc = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        Davhar::create(['orc_id' => $orc->id, 'number' => '1', 'order' => 1]);

        // Create some doors manually
        Toot::create(['korpus_id' => $korpus->id, 'number' => 1]);
        Toot::create(['korpus_id' => $korpus->id, 'number' => 2]);

        $statistics = $this->doorNumberingService->getKorpusStatistics($korpus);

        $this->assertEquals($korpus->id, $statistics['korpus_id']);
        $this->assertEquals(1, $statistics['numbering_type']);
        $this->assertEquals(1, $statistics['total_orcs']);
        $this->assertEquals(1, $statistics['total_davhars']);
        $this->assertEquals(2, $statistics['total_toots']);
        $this->assertEquals(6, $statistics['expected_toots']);
        $this->assertEquals(33.33, $statistics['completion_percentage']);
    }
}
