<?php

namespace App\Http\Requests\DoorNumbering;

use Illuminate\Foundation\Http\FormRequest;

class SetDavharDoorNumbersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Add authorization logic as needed
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'davhar_id' => 'required|integer|exists:davhars,id',
            'door_numbers' => 'required|array|min:1',
            'door_numbers.*' => 'required|integer|min:1',
            'validate_only' => 'boolean'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'davhar_id.required' => 'Davhar ID is required',
            'davhar_id.exists' => 'The specified Davhar does not exist',
            'door_numbers.required' => 'Door numbers are required',
            'door_numbers.array' => 'Door numbers must be an array',
            'door_numbers.min' => 'At least one door number must be provided',
            'door_numbers.*.required' => 'Each door number is required',
            'door_numbers.*.integer' => 'Each door number must be an integer',
            'door_numbers.*.min' => 'Each door number must be at least 1',
            'validate_only.boolean' => 'Validate only must be a boolean value'
        ];
    }
}
