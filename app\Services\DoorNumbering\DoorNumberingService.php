<?php

namespace App\Services\DoorNumbering;

use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use App\Services\DoorNumbering\Calculators\DoorNumberingCalculatorInterface;
use App\Services\DoorNumbering\Calculators\Type1Calculator;
use App\Services\DoorNumbering\Calculators\Type2Calculator;
use App\Services\DoorNumbering\Calculators\Type3Calculator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class DoorNumberingService
{
    /**
     * Get the appropriate calculator for the numbering type
     *
     * @param int $numberingType
     * @return DoorNumberingCalculatorInterface
     * @throws \InvalidArgumentException
     */
    protected function getCalculator(int $numberingType): DoorNumberingCalculatorInterface
    {
        return match ($numberingType) {
            1 => new Type1Calculator(),
            2 => new Type2Calculator(),
            3 => new Type3Calculator(),
            default => throw new \InvalidArgumentException("Unsupported numbering type: {$numberingType}")
        };
    }

    /**
     * Calculate and assign door numbers for a Korpus
     *
     * @param Korpus $korpus
     * @param bool $regenerateExisting Whether to regenerate existing door numbers
     * @return array Results of the operation
     */
    public function calculateKorpusDoorNumbers(Korpus $korpus, bool $regenerateExisting = false): array
    {
        Log::info('Starting door number calculation for Korpus', [
            'korpus_id' => $korpus->id,
            'numbering_type' => $korpus->numbering_type,
            'regenerate_existing' => $regenerateExisting
        ]);

        try {
            $calculator = $this->getCalculator($korpus->numbering_type);
            
            return DB::transaction(function () use ($korpus, $calculator, $regenerateExisting) {
                // Calculate door ranges for all levels
                $ranges = $calculator->calculateRanges($korpus);
                
                // Apply the calculated ranges
                $this->applyRanges($korpus, $ranges, $regenerateExisting);
                
                // Generate door numbers
                $doorResults = $this->generateDoorNumbers($korpus, $regenerateExisting);
                
                Log::info('Door number calculation completed successfully', [
                    'korpus_id' => $korpus->id,
                    'doors_created' => $doorResults['created'],
                    'doors_updated' => $doorResults['updated']
                ]);
                
                return [
                    'success' => true,
                    'korpus_id' => $korpus->id,
                    'numbering_type' => $korpus->numbering_type,
                    'ranges' => $ranges,
                    'doors' => $doorResults
                ];
            });
            
        } catch (\Exception $e) {
            Log::error('Error during door number calculation', [
                'korpus_id' => $korpus->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'korpus_id' => $korpus->id
            ];
        }
    }

    /**
     * Apply calculated ranges to Orcs and Davhars
     *
     * @param Korpus $korpus
     * @param array $ranges
     * @param bool $regenerateExisting
     * @return void
     */
    protected function applyRanges(Korpus $korpus, array $ranges, bool $regenerateExisting): void
    {
        // Apply Orc ranges
        foreach ($ranges['orcs'] as $orcData) {
            $orc = Orc::find($orcData['orc_id']);
            if ($orc) {
                $orc->update([
                    'begin_toot_number' => $orcData['begin_toot_number'],
                    'end_toot_number' => $orcData['end_toot_number']
                ]);
            }
        }

        // Apply Davhar ranges
        foreach ($ranges['davhars'] as $davharData) {
            $davhar = Davhar::find($davharData['davhar_id']);
            if ($davhar) {
                $updateData = [
                    'begin_toot_number' => $davharData['begin_toot_number'],
                    'end_toot_number' => $davharData['end_toot_number']
                ];
                
                // Add floor_number for Type 3
                if (isset($davharData['floor_number'])) {
                    $updateData['floor_number'] = $davharData['floor_number'];
                }
                
                $davhar->update($updateData);
            }
        }
    }

    /**
     * Generate door numbers based on calculated ranges
     *
     * @param Korpus $korpus
     * @param bool $regenerateExisting
     * @return array
     */
    protected function generateDoorNumbers(Korpus $korpus, bool $regenerateExisting): array
    {
        $created = 0;
        $updated = 0;

        foreach ($korpus->orcs as $orc) {
            foreach ($orc->davhars as $davhar) {
                if ($davhar->begin_toot_number && $davhar->end_toot_number) {
                    for ($doorNumber = $davhar->begin_toot_number; $doorNumber <= $davhar->end_toot_number; $doorNumber++) {
                        $existingToot = Toot::where('korpus_id', $korpus->id)
                            ->where('davhar_id', $davhar->id)
                            ->where('number', $doorNumber)
                            ->first();

                        if ($existingToot) {
                            if ($regenerateExisting) {
                                $existingToot->update(['number' => $doorNumber]);
                                $updated++;
                            }
                        } else {
                            Toot::create([
                                'korpus_id' => $korpus->id,
                                'davhar_id' => $davhar->id,
                                'number' => $doorNumber
                            ]);
                            $created++;
                        }
                    }
                }
            }
        }

        return [
            'created' => $created,
            'updated' => $updated
        ];
    }

    /**
     * Validate door numbering configuration for a Korpus
     *
     * @param Korpus $korpus
     * @return array Validation results
     */
    public function validateKorpusConfiguration(Korpus $korpus): array
    {
        try {
            $calculator = $this->getCalculator($korpus->numbering_type);
            return $calculator->validate($korpus);
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'errors' => [$e->getMessage()]
            ];
        }
    }

    /**
     * Get door numbering statistics for a Korpus
     *
     * @param Korpus $korpus
     * @return array
     */
    public function getKorpusStatistics(Korpus $korpus): array
    {
        $totalOrcs = $korpus->orcs()->count();
        $totalDavhars = Davhar::whereIn('orc_id', $korpus->orcs()->pluck('id'))->count();
        $totalToots = Toot::where('korpus_id', $korpus->id)->count();
        
        $expectedToots = 0;
        if ($korpus->numbering_type == 1) {
            $expectedToots = $korpus->end_toot_number - $korpus->begin_toot_number + 1;
        } else {
            foreach ($korpus->orcs as $orc) {
                if ($orc->end_toot_number && $orc->begin_toot_number) {
                    $expectedToots += $orc->end_toot_number - $orc->begin_toot_number + 1;
                }
            }
        }

        return [
            'korpus_id' => $korpus->id,
            'numbering_type' => $korpus->numbering_type,
            'total_orcs' => $totalOrcs,
            'total_davhars' => $totalDavhars,
            'total_toots' => $totalToots,
            'expected_toots' => $expectedToots,
            'completion_percentage' => $expectedToots > 0 ? round(($totalToots / $expectedToots) * 100, 2) : 0
        ];
    }
}
