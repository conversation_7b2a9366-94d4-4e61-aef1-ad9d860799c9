<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Korpus
 *
 * @property int $id
 * @property int $bair_id
 * @property string $name
 * @property int $order
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $begin_toot_number
 * @property int $end_toot_number
 * @property-read \App\Models\Bair $bair
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Orc> $orcs
 * @property-read int|null $orcs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Toot> $toots
 * @property-read int|null $toots_count
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus query()
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereBairId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereBeginTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereEndTootNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Korpus whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Korpus extends Model
{
    use HasFactory;

    const ID                = 'id';
    const BAIR_ID           = 'bair_id';
    const NAME              = 'name';
    const ORDER             = 'order';
    const BEGIN_TOOT_NUMBER = 'begin_toot_number';
    const END_TOOT_NUMBER   = 'end_toot_number';
    const NUMBERING_TYPE    = 'numbering_type';
    const DIGIT_MULTIPLIER  = 'digit_multiplier';
    const CODE              = 'code';

    const RELATION_BAIR = 'bair';
    const RELATION_ORCS = 'orcs';

    protected $fillable = [
        self::BAIR_ID,
        self::NAME,
        self::ORDER,
        self::BEGIN_TOOT_NUMBER,
        self::END_TOOT_NUMBER,
        self::NUMBERING_TYPE,
        self::DIGIT_MULTIPLIER,
        self::CODE
    ];

    public function bair()
    {
        return $this->belongsTo(Bair::class);
    }

    public function orcs()
    {
        return $this->hasMany(Orc::class);
    }

    public function toots()
    {
        return $this->hasMany(Toot::class)->orderBy('number', 'asc');
    }
}
