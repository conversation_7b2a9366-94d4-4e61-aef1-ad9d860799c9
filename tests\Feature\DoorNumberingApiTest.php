<?php

namespace Tests\Feature;

use App\Models\Sukh;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DoorNumberingApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user for authentication
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_active' => true
        ]);
    }

    /** @test */
    public function it_can_generate_door_numbers_via_api()
    {
        // Create test data
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 12
        ]);

        // Create Orcs and Davhars
        $orc1 = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $orc2 = Orc::create(['korpus_id' => $korpus->id, 'number' => '2']);
        Davhar::create(['orc_id' => $orc1->id, 'number' => '1', 'order' => 1]);
        Davhar::create(['orc_id' => $orc1->id, 'number' => '2', 'order' => 2]);
        Davhar::create(['orc_id' => $orc2->id, 'number' => '1', 'order' => 1]);
        Davhar::create(['orc_id' => $orc2->id, 'number' => '2', 'order' => 2]);

        // Clear any existing doors
        Toot::where('korpus_id', $korpus->id)->delete();

        // Make API request
        $response = $this->actingAs($this->user)
            ->postJson('/api/door-numbering/generate', [
                'korpus_id' => $korpus->id,
                'regenerate_existing' => false
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Door numbers generated successfully'
            ]);

        // Verify doors were created
        $this->assertEquals(12, Toot::where('korpus_id', $korpus->id)->count());
    }

    /** @test */
    public function it_can_validate_korpus_configuration_via_api()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 12
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/door-numbering/validate/{$korpus->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'korpus_id' => $korpus->id
            ]);
    }

    /** @test */
    public function it_can_get_korpus_statistics_via_api()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6
        ]);

        $orc = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $davhar = Davhar::create(['orc_id' => $orc->id, 'number' => '1', 'order' => 1]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/door-numbering/statistics/{$korpus->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'korpus_id' => $korpus->id,
                    'numbering_type' => 1,
                    'total_orcs' => 1,
                    'total_davhars' => 1
                ]
            ]);
    }

    /** @test */
    public function it_can_set_davhar_door_numbers_via_api()
    {
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 100
        ]);

        $orc = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
        $davhar = Davhar::create(['orc_id' => $orc->id, 'number' => '1', 'order' => 1]);

        $doorNumbers = [1, 2, 3, 4, 5, 6];

        $response = $this->actingAs($this->user)
            ->postJson('/api/door-numbering/set-davhar-doors', [
                'davhar_id' => $davhar->id,
                'door_numbers' => $doorNumbers,
                'validate_only' => false
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Door numbers set successfully'
            ]);

        // Verify doors were created
        $this->assertEquals(6, Toot::where('davhar_id', $davhar->id)->count());
        $createdNumbers = Toot::where('davhar_id', $davhar->id)->pluck('number')->sort()->values();
        $this->assertEquals(collect($doorNumbers), $createdNumbers);
    }

    /** @test */
    public function it_validates_api_requests()
    {
        // Test missing korpus_id
        $response = $this->actingAs($this->user)
            ->postJson('/api/door-numbering/generate', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['korpus_id']);

        // Test invalid davhar_id
        $response = $this->actingAs($this->user)
            ->postJson('/api/door-numbering/set-davhar-doors', [
                'davhar_id' => 999999,
                'door_numbers' => [1, 2, 3]
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['davhar_id']);
    }

    /** @test */
    public function it_handles_nonexistent_korpus()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/door-numbering/generate', [
                'korpus_id' => 999999
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['korpus_id']);
    }
}
