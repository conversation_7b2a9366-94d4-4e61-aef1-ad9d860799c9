<?php

namespace App\Services\DoorNumbering\Calculators;

use App\Models\Korpus;
use Illuminate\Support\Facades\Log;

/**
 * Type 2: Orc-wise Consecutive Numbering
 * 
 * This calculator handles numbering where each Orc has its own independent
 * numbering sequence starting from 1.
 */
class Type2Calculator implements DoorNumberingCalculatorInterface
{
    public function getNumberingType(): int
    {
        return 2;
    }

    public function calculateRanges(Korpus $korpus): array
    {
        Log::info('Type2Calculator: Starting range calculation', [
            'korpus_id' => $korpus->id
        ]);

        $orcs = $korpus->orcs()->orderBy('number')->get();
        $orcRanges = [];
        $davharRanges = [];

        foreach ($orcs as $orc) {
            // For Type 2, each Orc starts from 1
            // We need to determine the total doors for this Orc
            $davhars = $orc->davhars()->orderBy('order')->get();
            $totalDavhars = $davhars->count();

            if ($totalDavhars === 0) {
                continue;
            }

            // Calculate total doors for this Orc based on existing configuration
            // or use a default calculation
            $totalOrcDoors = $this->calculateOrcTotalDoors($orc);

            $orcRanges[] = [
                'orc_id' => $orc->id,
                'begin_toot_number' => 1,
                'end_toot_number' => $totalOrcDoors
            ];

            // Calculate Davhar ranges within this Orc
            $doorsPerDavhar = intval($totalOrcDoors / $totalDavhars);
            $remainingDoors = $totalOrcDoors % $totalDavhars;
            $currentDoorNumber = 1;

            foreach ($davhars as $index => $davhar) {
                // Calculate doors for this Davhar (distribute remaining doors to first Davhars)
                $davharDoors = $doorsPerDavhar + ($index < $remainingDoors ? 1 : 0);
                $davharBegin = $currentDoorNumber;
                $davharEnd = $currentDoorNumber + $davharDoors - 1;

                $davharRanges[] = [
                    'davhar_id' => $davhar->id,
                    'orc_id' => $orc->id,
                    'begin_toot_number' => $davharBegin,
                    'end_toot_number' => $davharEnd
                ];

                $currentDoorNumber += $davharDoors;
            }
        }

        Log::info('Type2Calculator: Range calculation completed', [
            'korpus_id' => $korpus->id,
            'total_orcs' => count($orcRanges),
            'orc_ranges_count' => count($orcRanges),
            'davhar_ranges_count' => count($davharRanges)
        ]);

        return [
            'orcs' => $orcRanges,
            'davhars' => $davharRanges
        ];
    }

    /**
     * Calculate total doors for an Orc in Type 2 numbering
     *
     * @param \App\Models\Orc $orc
     * @return int
     */
    protected function calculateOrcTotalDoors($orc): int
    {
        // If the Orc already has door range configured, use it
        if ($orc->end_toot_number && $orc->begin_toot_number) {
            return $orc->end_toot_number - $orc->begin_toot_number + 1;
        }

        // Otherwise, calculate based on Davhars
        $davharsCount = $orc->davhars()->count();
        
        // Default: 6 doors per Davhar (can be made configurable)
        $defaultDoorsPerDavhar = 6;
        
        return $davharsCount * $defaultDoorsPerDavhar;
    }

    public function validate(Korpus $korpus): array
    {
        $errors = [];

        // Check if there are Orcs
        $orcsCount = $korpus->orcs()->count();
        if ($orcsCount === 0) {
            $errors[] = 'At least one Orc must exist for door numbering';
        }

        // Check if all Orcs have Davhars
        $orcsWithoutDavhars = $korpus->orcs()->whereDoesntHave('davhars')->count();
        if ($orcsWithoutDavhars > 0) {
            $errors[] = "Found {$orcsWithoutDavhars} Orc(s) without Davhars";
        }

        // For Type 2, we don't need Korpus-level door ranges
        // Each Orc is independent

        // Validate each Orc has reasonable configuration
        foreach ($korpus->orcs as $orc) {
            $davharsCount = $orc->davhars()->count();
            if ($davharsCount === 0) {
                $errors[] = "Orc {$orc->number} has no Davhars";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
