<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\Constant\ConstData;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if(User::count() > 0){
            return;
        }

        $users = array (
                    'users' =>
                        array (
                            0 =>
                                array (
                                    'id'        => 1,
                                    'name'      => 'superadmin',
                                    'phone'     => '99751064',
                                    'email'     => '<EMAIL>',
                                    'password'  => '$2y$10$5OMbpq3wC4D8DDdFEtzmj.fBrpqE/vADDQxpCNbjJ9B5XLpnq/6x2',
                                    'is_active' => true,
                                ),

                        ),
                );

        foreach($users['users'] as $user){
            $superUser = User::create([
                'id'          => $user['id'],
                'name'        => $user['name'],
                'phone'       => $user['phone'],
                'email'       => $user['email'],
                'password'    => $user['password'],
                'is_active'   => $user['is_active'],
            ]);
            $sadminRole = Role::where(Role::NAME, ConstData::ROLE_SUPER_ADMIN)->first();
            $superUser->roles()->attach($sadminRole->id);
        }

        // Reset the auto-increment sequence to prevent duplicate key errors
        $maxId = User::max('id') ?? 0;
        DB::statement("SELECT setval('users_id_seq', " . ($maxId + 1) . ", false);");
    }
}
